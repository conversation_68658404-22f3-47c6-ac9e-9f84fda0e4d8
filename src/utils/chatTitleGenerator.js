/**
 * Utility functions for generating meaningful chat titles
 */

// Common generic titles that should be replaced
const GENERIC_TITLES = [
  'New Chat',
  'hello',
  'hi',
  'hey',
  'test',
  'testing',
  'chat',
  'conversation'
];

/**
 * Check if a title is generic and should be replaced
 * @param {string} title - The current chat title
 * @returns {boolean} True if the title is generic
 */
export const isGenericTitle = (title) => {
  if (!title || typeof title !== 'string') return true;
  
  const normalizedTitle = title.toLowerCase().trim();
  
  // Check if it's in the generic list
  if (GENERIC_TITLES.includes(normalizedTitle)) return true;
  
  // Check if it's just a greeting
  const greetingPattern = /^(hi|hello|hey|hiya|howdy)[\s!.]*$/i;
  if (greetingPattern.test(normalizedTitle)) return true;
  
  // Check if it's very short and likely not meaningful
  if (normalizedTitle.length <= 2) return true;
  
  return false;
};

/**
 * Generate a meaningful title from conversation context
 * @param {Array} messages - Array of messages to analyze
 * @returns {string} Generated title
 */
export const generateTitleFromConversation = (messages) => {
  if (!messages || !Array.isArray(messages) || messages.length === 0) {
    return 'New Chat';
  }

  // Get the first user message and first assistant response
  const userMessage = messages.find(msg => msg.role === 'user');
  const assistantMessage = messages.find(msg => msg.role === 'assistant');

  if (!userMessage) {
    return 'New Chat';
  }

  // Analyze the conversation context
  const userContent = userMessage.content.trim();
  const assistantContent = assistantMessage ? assistantMessage.content.trim() : '';

  return generateContextualTitle(userContent, assistantContent);
};

/**
 * Generate a meaningful title from a single message (fallback)
 * @param {string} message - The user message to generate title from
 * @returns {string} Generated title
 */
export const generateTitleFromMessage = (message) => {
  if (!message || typeof message !== 'string') {
    return 'New Chat';
  }

  return generateContextualTitle(message, '');
};

/**
 * Generate title specifically from the first 4 messages for optimal context
 * @param {Array} messages - Array of exactly 4 messages (2 user + 2 assistant)
 * @returns {string} Generated title
 */
export const generateTitleFromFirstFourMessages = (messages) => {
  if (!messages || !Array.isArray(messages) || messages.length < 4) {
    return 'New Chat';
  }

  // Get the first 4 messages for analysis
  const firstFourMessages = messages.slice(0, 4);

  // Extract user and assistant messages
  const userMessages = firstFourMessages.filter(msg => msg.role === 'user');
  const assistantMessages = firstFourMessages.filter(msg => msg.role === 'assistant');

  if (userMessages.length < 2 || assistantMessages.length < 2) {
    // Fallback to regular conversation analysis
    return generateTitleFromConversation(messages);
  }

  // Combine content from first two user messages and first two assistant responses
  const combinedUserContent = userMessages.slice(0, 2).map(msg => msg.content).join(' ');
  const combinedAssistantContent = assistantMessages.slice(0, 2).map(msg => msg.content).join(' ');

  // Generate title with enhanced context from 4 messages
  return generateContextualTitle(combinedUserContent, combinedAssistantContent);
};

/**
 * Generate contextual title based on user message and assistant response
 * @param {string} userMessage - The user's message
 * @param {string} assistantMessage - The assistant's response (optional)
 * @returns {string} Generated title
 */
const generateContextualTitle = (userMessage, assistantMessage = '') => {
  // Clean the user message
  let cleanMessage = userMessage.trim();
  
  // Analyze conversation context for better title generation
  const context = analyzeConversationContext(userMessage, assistantMessage);

  if (context.title) {
    return context.title;
  }

  // Remove common prefixes
  const prefixesToRemove = [
    /^(can you|could you|please|would you|will you)\s+/i,
    /^(i want to|i need to|i would like to|i'd like to)\s+/i,
    /^(help me|show me|tell me|explain)\s+/i,
    /^(what is|what are|how do|how can|where is)\s+/i
  ];

  prefixesToRemove.forEach(pattern => {
    cleanMessage = cleanMessage.replace(pattern, '');
  });

  // Remove question marks and exclamation points from the end
  cleanMessage = cleanMessage.replace(/[?!]+$/, '');

  // Capitalize first letter
  cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);

  // Truncate if too long
  if (cleanMessage.length > 50) {
    // Try to break at a word boundary
    const truncated = cleanMessage.substring(0, 47);
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > 20) {
      cleanMessage = truncated.substring(0, lastSpace) + '...';
    } else {
      cleanMessage = truncated + '...';
    }
  }

  // If the cleaned message is too short or generic, use a fallback
  if (cleanMessage.length < 3 || isGenericTitle(cleanMessage)) {
    return generateFallbackTitle(userMessage, assistantMessage);
  }

  return cleanMessage;
};

/**
 * Analyze conversation context to generate intelligent titles
 * @param {string} userMessage - The user's message
 * @param {string} assistantMessage - The assistant's response
 * @returns {Object} Analysis result with title suggestion
 */
const analyzeConversationContext = (userMessage, assistantMessage = '') => {
  const userLower = userMessage.toLowerCase();
  const assistantLower = assistantMessage.toLowerCase();

  // Database and SQL related conversations
  if (userLower.includes('select') || userLower.includes('sql') || userLower.includes('query')) {
    if (assistantLower.includes('result') || assistantLower.includes('data')) {
      return { title: 'SQL Query Analysis' };
    }
    return { title: 'Database Query' };
  }

  // Table and schema exploration
  if (userLower.includes('table') || userLower.includes('schema') || userLower.includes('column')) {
    if (assistantLower.includes('relationship') || assistantLower.includes('foreign key')) {
      return { title: 'Database Schema Analysis' };
    }
    return { title: 'Table Structure' };
  }

  // Data analysis and reporting
  if (userLower.includes('report') || userLower.includes('analysis') || userLower.includes('trend')) {
    return { title: 'Data Analysis' };
  }

  // Performance and optimization
  if (userLower.includes('performance') || userLower.includes('optimize') || userLower.includes('slow')) {
    return { title: 'Performance Optimization' };
  }

  // User management and permissions
  if (userLower.includes('user') || userLower.includes('permission') || userLower.includes('access')) {
    return { title: 'User Management' };
  }

  // Data migration and import/export
  if (userLower.includes('import') || userLower.includes('export') || userLower.includes('migration')) {
    return { title: 'Data Migration' };
  }

  // Troubleshooting and errors
  if (userLower.includes('error') || userLower.includes('problem') || userLower.includes('issue')) {
    return { title: 'Troubleshooting' };
  }

  return { title: null };
};

/**
 * Generate a fallback title when the message doesn't provide good content
 * @param {string} userMessage - The user's message
 * @param {string} assistantMessage - The assistant's response (optional)
 * @returns {string} Fallback title
 */
const generateFallbackTitle = (userMessage, assistantMessage = '') => {
  // Try to detect the type of query/conversation
  const lowerMessage = userMessage.toLowerCase();
  const lowerAssistant = assistantMessage.toLowerCase();

  // Database-related queries
  if (lowerMessage.includes('select') || lowerMessage.includes('query') || lowerMessage.includes('database')) {
    return 'Database Query';
  }

  if (lowerMessage.includes('table') || lowerMessage.includes('column')) {
    return 'Table Analysis';
  }

  if (lowerMessage.includes('data') || lowerMessage.includes('record')) {
    return 'Data Exploration';
  }

  // General conversation types
  if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
    return 'Help Request';
  }

  if (lowerMessage.includes('explain') || lowerMessage.includes('what')) {
    return 'Question';
  }

  // Use assistant response context if available
  if (assistantMessage) {
    if (lowerAssistant.includes('database') || lowerAssistant.includes('query')) {
      return 'Database Discussion';
    }
    if (lowerAssistant.includes('help') || lowerAssistant.includes('assist')) {
      return 'Assistant Help';
    }
  }

  // Extract key nouns from the message for a more meaningful title
  const keyWords = extractKeyWords(userMessage);
  if (keyWords.length > 0) {
    return keyWords.slice(0, 2).map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ') + ' Discussion';
  }

  // Last resort: use a descriptive fallback
  return 'Chat Discussion';
};

/**
 * Extract key words from a message for title generation
 * @param {string} message - The message to analyze
 * @returns {Array} Array of key words
 */
const extractKeyWords = (message) => {
  // Remove common words and extract meaningful terms
  const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'];

  const words = message.toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove punctuation
    .split(/\s+/)
    .filter(word => word.length > 2 && !commonWords.includes(word))
    .slice(0, 3); // Take first 3 meaningful words

  return words;
};

/**
 * Smart title update - only update if current title is generic and we have 4 messages
 * @param {string} currentTitle - Current chat title
 * @param {Array} messages - All messages in the chat
 * @returns {string|null} New title if update is needed, null if no update needed
 */
export const getUpdatedTitleIfNeeded = (currentTitle, messages) => {
  if (!isGenericTitle(currentTitle)) {
    return null; // Don't update non-generic titles
  }

  // Wait for exactly 4 messages (2 user + 2 assistant) for optimal context
  if (!messages || messages.length < 4) {
    return null;
  }

  // Only generate title once when we reach 4 messages
  if (messages.length === 4) {
    return generateTitleFromFirstFourMessages(messages);
  }

  return null; // Don't update after 4 messages to preserve the generated title
};

/**
 * Batch process multiple chats to update generic titles
 * @param {Array} chats - Array of chat objects
 * @param {Object} messages - Messages object keyed by chat ID
 * @returns {Array} Array of {chatId, newTitle} objects for chats that need updates
 */
export const getBatchTitleUpdates = (chats, messages) => {
  const updates = [];

  chats.forEach(chat => {
    if (isGenericTitle(chat.title)) {
      const chatMessages = messages[chat.id] || [];

      // Only update if we have at least 4 messages for optimal context
      if (chatMessages.length >= 4) {
        const newTitle = generateTitleFromFirstFourMessages(chatMessages);
        if (newTitle !== chat.title) {
          updates.push({
            chatId: chat.id,
            currentTitle: chat.title,
            newTitle: newTitle
          });
        }
      }
    }
  });

  return updates;
};
