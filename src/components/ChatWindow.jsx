import { useEffect, useRef } from 'react';

const ChatWindow = ({
  currentChat,
  displayMessages,
  isSubmitting,
  prompt,
  setPrompt,
  handleSend,
  queryResult,
  showQueryResult,
  setShowQueryResult
}) => {
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [displayMessages]);

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const formatMessage = (content) => {
    // Handle undefined or null content
    if (!content) {
      return <div>No content</div>;
    }

    // Simple formatting for code blocks and line breaks
    return content
      .split('\n')
      .map((line, index) => (
        <div key={index}>
          {line.startsWith('```') ? (
            <code className="code-block">{line.replace(/```/g, '')}</code>
          ) : (
            line
          )}
        </div>
      ));
  };

  return (
    <div className="chat-window">
      <div className="chat-messages">
        {currentChat ? (
          <>
            {displayMessages.length === 0 ? (
              <div className="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  <path d="M12 7v6M9 10l3-3 3 3"/>
                </svg>
                <h3>Start a conversation</h3>
                <p>Send a message to begin your chat with the AI assistant.</p>
              </div>
            ) : (
              <>
                {displayMessages.map((message, index) => (
                  <div key={index} className={`message ${message.role}`}>
                    <div className="message-wrapper">
                      <div className="message-content">
                        {formatMessage(message.content)}
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}
            
            {isSubmitting && (
              <div className="message assistant">
                <div className="message-wrapper">
                  <div className="message-content">
                    <div className="loading-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        ) : (
          <div className="empty-state">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              <path d="M8 10h8M8 14h6"/>
            </svg>
            <h3>Select a chat or create a new one</h3>
            <p>Choose an existing chat from the sidebar or create a new one to get started.</p>
          </div>
        )}
      </div>

      {queryResult && showQueryResult && (
        <div className="query-result-modal">
          <div className="query-result-content">
            <div className="query-result-header">
              <h3>Query Result</h3>
              <button
                onClick={() => setShowQueryResult(false)}
                className="close-button"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M18 6L6 18M6 6l12 12"/>
                </svg>
              </button>
            </div>
            <div className="query-result-data">
              <pre>{JSON.stringify(queryResult, null, 2)}</pre>
            </div>
          </div>
        </div>
      )}

      <div className="input-container">
        <div className="input-wrapper">
          <div className="message-input-container">
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={currentChat ? "Type your message..." : "Type your message to start a new chat..."}
              disabled={isSubmitting}
              rows={1}
              className="message-input"
            />
            <button
              className="send-button"
              title="Send message (Enter)"
              onClick={handleSend}
              disabled={!prompt.trim() || isSubmitting}
            >
              {isSubmitting ? (
                <div className="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              ) : (
                <svg viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="4.0"
                style={{ width: '40px', height: '40px', transform: 'scale(2.0)', transformOrigin: 'center' }}>
                  <path d="M12 19V5"/>
                  <path d="M5 12l7-7 7 7"/>
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;
