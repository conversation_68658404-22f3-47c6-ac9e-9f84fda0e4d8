import { useState, memo, useMemo, useEffect, useRef, useCallback } from 'react';

// Memoized menu component to prevent re-renders
const ChatMenu = memo(({ chat, onAction, isArchived, position }) => {
  const handleClick = useCallback((action, event) => {
    event.stopPropagation();
    event.preventDefault();
    onAction(action, chat);
  }, [onAction, chat]);

  return (
    <div
      className="chat-menu"
      style={{
        position: 'fixed',
        left: `${position.left}px`,
        top: `${position.top}px`,
        zIndex: 999999,
        display: 'block'
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {!isArchived ? (
        <>
          <button onClick={(e) => handleClick('rename', e)}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
            Rename
          </button>
          <button onClick={(e) => handleClick('share', e)}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
              <polyline points="16,6 12,2 8,6"/>
              <line x1="12" y1="2" x2="12" y2="15"/>
            </svg>
            Share
          </button>
          <button onClick={(e) => handleClick('archive', e)}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <path d="M9 9h6v6H9z"/>
            </svg>
            Archive
          </button>
        </>
      ) : (
        <button onClick={(e) => handleClick('unarchive', e)}>
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M21 8.5l-10 5.5-10-5.5"/>
            <path d="M21 16.5l-10 5.5-10-5.5"/>
            <path d="M21 12.5l-10 5.5-10-5.5"/>
          </svg>
          Unarchive
        </button>
      )}
      <button onClick={(e) => handleClick('delete', e)} className="delete-option">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
          <path d="M10 11v6M14 11v6"/>
        </svg>
        Delete
      </button>
    </div>
  );
});

const ChatSidebar = ({
  chatLoading,
  chats,
  currentChat,
  setCurrentChat,
  switchToChat,
  loadMessages,
  createNewChat,
  canCreateNewChat,
  updateChatTitle,
  deleteChat,
  archiveChat,
  unarchiveChat,
  messages
}) => {
  const [editingChat, setEditingChat] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [openMenuChat, setOpenMenuChat] = useState(null);
  const sidebarRef = useRef(null);



  const handleChatSelect = async (chat) => {
    // Avoid unnecessary re-renders if the same chat is selected
    if (currentChat && currentChat.id === chat.id) {
      return;
    }

    // Use switchToChat for smart empty chat cleanup
    await switchToChat(chat);

    // Only load messages if they haven't been loaded yet
    const chatMessages = messages ? messages[chat.id] : null;
    if (!chatMessages || chatMessages.length === 0) {
      try {
        await loadMessages(chat.id);
      } catch (error) {
        console.error('Failed to load messages:', error);
      }
    }
  };

  const handleEditStart = (chat) => {
    setEditingChat(chat.id);
    setEditTitle(chat.title);
  };

  const handleEditSave = async () => {
    if (editingChat && editTitle.trim()) {
      try {
        await updateChatTitle(editingChat, editTitle.trim());
        setEditingChat(null);
      } catch (error) {
        console.error('Failed to update chat title:', error);
      }
    }
  };

  const handleEditCancel = () => {
    setEditingChat(null);
    setEditTitle('');
  };

  const handleDelete = async (chatId) => {
    if (window.confirm('Are you sure you want to delete this chat?')) {
      try {
        await deleteChat(chatId);
        setOpenMenuChat(null); // Close menu after action
      } catch (error) {
        console.error('Failed to delete chat:', error);
      }
    }
  };

  const handleShare = async (chatId) => {
    try {
      // Create a shareable link or copy chat content
      const shareUrl = `${window.location.origin}/chat/${chatId}`;
      await navigator.clipboard.writeText(shareUrl);
      alert('Chat link copied to clipboard!');
      setOpenMenuChat(null); // Close menu after action
    } catch (error) {
      console.error('Failed to share chat:', error);
      alert('Failed to copy link to clipboard');
    }
  };

  const handleMenuAction = useCallback((action, chat) => {
    switch (action) {
      case 'rename':
        handleEditStart(chat);
        setOpenMenuChat(null);
        break;
      case 'archive':
        archiveChat(chat.id);
        setOpenMenuChat(null);
        break;
      case 'unarchive':
        unarchiveChat(chat.id);
        setOpenMenuChat(null);
        break;
      case 'delete':
        handleDelete(chat.id);
        break;
      case 'share':
        handleShare(chat.id);
        break;
      default:
        setOpenMenuChat(null);
    }
  }, [archiveChat, unarchiveChat, handleEditStart, handleDelete, handleShare]);

  const toggleMenu = useCallback((chatId, event) => {
    event.stopPropagation();
    event.preventDefault();

    if (openMenuChat === chatId) {
      setOpenMenuChat(null);
      return;
    }

    // Calculate position immediately
    const buttonRect = event.currentTarget.getBoundingClientRect();
    let left = buttonRect.right + 8;
    let top = buttonRect.top;

    // Check if menu would go off-screen
    const menuWidth = 140;
    if (left + menuWidth > window.innerWidth) {
      left = buttonRect.left - menuWidth - 8;
    }

    setMenuPosition({ left, top });
    setOpenMenuChat(chatId);
  }, [openMenuChat]);

  // Close menu when clicking outside
  const handleChatClick = useCallback((chat) => {
    setOpenMenuChat(null);
    handleChatSelect(chat);
  }, [handleChatSelect]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if click is outside both sidebar and menu
      const isClickInSidebar = sidebarRef.current && sidebarRef.current.contains(event.target);
      const isClickInMenu = event.target.closest('.chat-menu');

      if (!isClickInSidebar && !isClickInMenu) {
        setOpenMenuChat(null);
      }
    };

    if (openMenuChat) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [openMenuChat]);

  // Memoize filtered chats to prevent unnecessary re-calculations
  const activeChats = useMemo(() => chats.filter(chat => !chat.archived), [chats]);
  const archivedChats = useMemo(() => chats.filter(chat => chat.archived), [chats]);

  return (
    <div className="sidebar" ref={sidebarRef}>
      <div className="new-chat-button-container">
        <button
          onClick={createNewChat}
          className="new-chat-button ripple"
          disabled={chatLoading || !canCreateNewChat}
          title={canCreateNewChat ? "Start new chat" : "Complete current chat or switch to another chat first"}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
          <span>New Chat</span>
        </button>
      </div>

      <div className="sidebar-header">
        <h2>Chats</h2>
        {chatLoading && <div className="loading-spinner">⟳</div>}
      </div>

      <div className="chat-list">
        {chatLoading ? (
          <div className="loading">
            <div className="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            Loading chats...
          </div>
        ) : (
          <>
            {activeChats.map(chat => (
              <div
                key={chat.id}
                className={`chat-item ${currentChat?.id === chat.id ? 'active' : ''}`}
                onClick={() => handleChatClick(chat)}
              >
                {editingChat === chat.id ? (
                  <div className="edit-chat">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleEditSave()}
                      onBlur={handleEditSave}
                      autoFocus
                    />
                  </div>
                ) : (
                  <>
                    <span className="chat-title">{chat.title}</span>
                    <div className="chat-actions">
                      <button
                        onClick={(e) => toggleMenu(chat.id, e)}
                        className="menu-btn"
                        title="Chat options"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <circle cx="12" cy="12" r="1"/>
                          <circle cx="12" cy="5" r="1"/>
                          <circle cx="12" cy="19" r="1"/>
                        </svg>
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}

            {archivedChats.length > 0 && (
              <div className="archived-section">
                <h3>Archived</h3>
                {archivedChats.map(chat => (
                  <div
                    key={chat.id}
                    className="chat-item archived"
                    onClick={() => handleChatClick(chat)}
                  >
                    <span className="chat-title">{chat.title}</span>
                    <div className="chat-actions">
                      <button
                        onClick={(e) => toggleMenu(chat.id, e)}
                        className="menu-btn"
                        title="Chat options"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <circle cx="12" cy="12" r="1"/>
                          <circle cx="12" cy="5" r="1"/>
                          <circle cx="12" cy="19" r="1"/>
                        </svg>
                      </button>

                      {openMenuChat === chat.id && (
                        <ChatMenu
                          chat={chat}
                          onAction={handleMenuAction}
                          isArchived={true}
                          position={menuPosition}
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Custom comparison function to prevent unnecessary re-renders
const areEqual = (prevProps, nextProps) => {
  return (
    prevProps.chatLoading === nextProps.chatLoading &&
    prevProps.chats.length === nextProps.chats.length &&
    prevProps.currentChat?.id === nextProps.currentChat?.id &&
    prevProps.canCreateNewChat === nextProps.canCreateNewChat &&
    JSON.stringify(prevProps.chats.map(c => ({ id: c.id, title: c.title, archived: c.archived }))) ===
    JSON.stringify(nextProps.chats.map(c => ({ id: c.id, title: c.title, archived: c.archived })))
  );
};

export default memo(ChatSidebar, areEqual);