<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Management Features Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Chat Management Features Test</h1>
    <p>This page tests the new chat management features implemented in your SaaS frontend.</p>

    <div class="test-section">
        <h2>Test 1: Empty Chat Detection</h2>
        <div class="test-case">
            <h3>isChatEmpty Function Test</h3>
            <p>Testing the function that detects if a chat has no user messages.</p>
            <button onclick="testIsChatEmpty()">Run Test</button>
            <div id="emptyChatResult"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Title Generation from 4 Messages</h2>
        <div class="test-case">
            <h3>generateTitleFromFirstFourMessages Function Test</h3>
            <p>Testing title generation specifically from the first 4 messages.</p>
            <button onclick="testTitleGeneration()">Run Test</button>
            <div id="titleGenerationResult"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: Smart Title Update Logic</h2>
        <div class="test-case">
            <h3>getUpdatedTitleIfNeeded Function Test</h3>
            <p>Testing that titles are only updated when we have exactly 4 messages.</p>
            <button onclick="testSmartTitleUpdate()">Run Test</button>
            <div id="smartTitleResult"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 4: Multiple Empty Chats Prevention</h2>
        <div class="test-case">
            <h3>canCreateNewChat Logic Test</h3>
            <p>Testing that only one empty chat can exist at a time.</p>
            <button onclick="testMultipleEmptyChats()">Run Test</button>
            <div id="multipleEmptyResult"></div>
        </div>
    </div>

    <script>
        // Mock the chat title generator functions for testing
        const isGenericTitle = (title) => {
            const genericTitles = ['New Chat', 'Chat Discussion', 'Untitled Chat'];
            return genericTitles.includes(title) || title.startsWith('New Chat');
        };

        const generateContextualTitle = (userMessage, assistantMessage = '') => {
            let cleanMessage = userMessage.trim();
            
            // Remove common prefixes
            cleanMessage = cleanMessage.replace(/^(hi|hello|hey|please|can you|could you|would you|i need|i want|help me|show me)\s+/i, '');
            
            // Remove question marks and exclamation points from the end
            cleanMessage = cleanMessage.replace(/[?!]+$/, '');
            
            // Capitalize first letter
            cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
            
            // Truncate if too long
            if (cleanMessage.length > 50) {
                const truncated = cleanMessage.substring(0, 47);
                const lastSpace = truncated.lastIndexOf(' ');
                if (lastSpace > 20) {
                    cleanMessage = truncated.substring(0, lastSpace) + '...';
                } else {
                    cleanMessage = truncated + '...';
                }
            }
            
            if (cleanMessage.length < 3) {
                return 'Chat Discussion';
            }
            
            return cleanMessage;
        };

        const generateTitleFromFirstFourMessages = (messages) => {
            if (!messages || !Array.isArray(messages) || messages.length < 4) {
                return 'New Chat';
            }

            const firstFourMessages = messages.slice(0, 4);
            const userMessages = firstFourMessages.filter(msg => msg.role === 'user');
            const assistantMessages = firstFourMessages.filter(msg => msg.role === 'assistant');

            if (userMessages.length < 2 || assistantMessages.length < 2) {
                return 'Chat Discussion';
            }

            const combinedUserContent = userMessages.slice(0, 2).map(msg => msg.content).join(' ');
            const combinedAssistantContent = assistantMessages.slice(0, 2).map(msg => msg.content).join(' ');

            return generateContextualTitle(combinedUserContent, combinedAssistantContent);
        };

        const getUpdatedTitleIfNeeded = (currentTitle, messages) => {
            if (!isGenericTitle(currentTitle)) {
                return null;
            }

            if (!messages || messages.length < 4) {
                return null;
            }

            if (messages.length === 4) {
                return generateTitleFromFirstFourMessages(messages);
            }

            return null;
        };

        // Test functions
        function testIsChatEmpty() {
            const results = [];
            
            // Test case 1: Empty chat (no messages)
            const emptyMessages = [];
            const isEmpty1 = !emptyMessages.some(msg => msg.role === 'user');
            results.push({
                test: "Empty chat with no messages",
                expected: true,
                actual: isEmpty1,
                pass: isEmpty1 === true
            });

            // Test case 2: Chat with only assistant messages
            const assistantOnlyMessages = [
                { role: 'assistant', content: 'Hello! How can I help you?' }
            ];
            const isEmpty2 = !assistantOnlyMessages.some(msg => msg.role === 'user');
            results.push({
                test: "Chat with only assistant messages",
                expected: true,
                actual: isEmpty2,
                pass: isEmpty2 === true
            });

            // Test case 3: Chat with user messages
            const userMessages = [
                { role: 'user', content: 'Hello' },
                { role: 'assistant', content: 'Hi there!' }
            ];
            const isEmpty3 = !userMessages.some(msg => msg.role === 'user');
            results.push({
                test: "Chat with user messages",
                expected: false,
                actual: isEmpty3,
                pass: isEmpty3 === false
            });

            displayResults('emptyChatResult', results);
        }

        function testTitleGeneration() {
            const results = [];

            // Test case 1: Exactly 4 messages
            const fourMessages = [
                { role: 'user', content: 'What is machine learning?' },
                { role: 'assistant', content: 'Machine learning is a subset of AI...' },
                { role: 'user', content: 'Can you give me an example?' },
                { role: 'assistant', content: 'Sure! A common example is...' }
            ];
            const title1 = generateTitleFromFirstFourMessages(fourMessages);
            results.push({
                test: "Generate title from 4 messages",
                expected: "Should contain 'machine learning'",
                actual: title1,
                pass: title1.toLowerCase().includes('machine learning') || title1.toLowerCase().includes('what is')
            });

            // Test case 2: Less than 4 messages
            const twoMessages = [
                { role: 'user', content: 'Hello' },
                { role: 'assistant', content: 'Hi there!' }
            ];
            const title2 = generateTitleFromFirstFourMessages(twoMessages);
            results.push({
                test: "Generate title from 2 messages",
                expected: "New Chat",
                actual: title2,
                pass: title2 === 'New Chat'
            });

            displayResults('titleGenerationResult', results);
        }

        function testSmartTitleUpdate() {
            const results = [];

            // Test case 1: Generic title with exactly 4 messages
            const fourMessages = [
                { role: 'user', content: 'How do I create a database?' },
                { role: 'assistant', content: 'To create a database, you can...' },
                { role: 'user', content: 'What about SQL tables?' },
                { role: 'assistant', content: 'SQL tables are created using...' }
            ];
            const newTitle1 = getUpdatedTitleIfNeeded('New Chat', fourMessages);
            results.push({
                test: "Update generic title with 4 messages",
                expected: "Should return new title",
                actual: newTitle1,
                pass: newTitle1 !== null && newTitle1 !== 'New Chat'
            });

            // Test case 2: Generic title with 3 messages
            const threeMessages = fourMessages.slice(0, 3);
            const newTitle2 = getUpdatedTitleIfNeeded('New Chat', threeMessages);
            results.push({
                test: "Don't update with only 3 messages",
                expected: null,
                actual: newTitle2,
                pass: newTitle2 === null
            });

            // Test case 3: Non-generic title
            const newTitle3 = getUpdatedTitleIfNeeded('Custom Chat Title', fourMessages);
            results.push({
                test: "Don't update non-generic title",
                expected: null,
                actual: newTitle3,
                pass: newTitle3 === null
            });

            // Test case 4: Generic title with 5 messages
            const fiveMessages = [...fourMessages, { role: 'user', content: 'Another question' }];
            const newTitle4 = getUpdatedTitleIfNeeded('New Chat', fiveMessages);
            results.push({
                test: "Don't update after 4 messages",
                expected: null,
                actual: newTitle4,
                pass: newTitle4 === null
            });

            displayResults('smartTitleResult', results);
        }

        function testMultipleEmptyChats() {
            const results = [];

            // Mock chat data
            const chats = [
                { id: 1, title: 'Existing Chat' },
                { id: 2, title: 'New Chat' }
            ];
            
            const messages = {
                1: [
                    { role: 'user', content: 'Hello' },
                    { role: 'assistant', content: 'Hi!' }
                ],
                2: [] // Empty chat
            };

            // Mock isChatEmpty function
            const isChatEmpty = (chatId) => {
                const chatMessages = messages[chatId] || [];
                return !chatMessages.some(msg => msg.role === 'user');
            };

            // Test case 1: Should not allow new chat when empty chat exists
            const hasEmptyChat = chats.some(chat => isChatEmpty(chat.id));
            const canCreate1 = !hasEmptyChat;
            results.push({
                test: "Prevent new chat when empty chat exists",
                expected: false,
                actual: canCreate1,
                pass: canCreate1 === false
            });

            // Test case 2: Should allow new chat when no empty chats exist
            const chatsWithoutEmpty = [
                { id: 1, title: 'Chat 1' },
                { id: 3, title: 'Chat 2' }
            ];
            const messagesWithoutEmpty = {
                1: [{ role: 'user', content: 'Hello' }],
                3: [{ role: 'user', content: 'Hi there' }]
            };
            const hasEmptyChat2 = chatsWithoutEmpty.some(chat => {
                const chatMessages = messagesWithoutEmpty[chat.id] || [];
                return !chatMessages.some(msg => msg.role === 'user');
            });
            const canCreate2 = !hasEmptyChat2;
            results.push({
                test: "Allow new chat when no empty chats exist",
                expected: true,
                actual: canCreate2,
                pass: canCreate2 === true
            });

            displayResults('multipleEmptyResult', results);
        }

        function displayResults(elementId, results) {
            const element = document.getElementById(elementId);
            let html = '';
            
            results.forEach(result => {
                const className = result.pass ? 'pass' : 'fail';
                const status = result.pass ? 'PASS' : 'FAIL';
                html += `
                    <div class="result ${className}">
                        <strong>${status}:</strong> ${result.test}<br>
                        <strong>Expected:</strong> ${result.expected}<br>
                        <strong>Actual:</strong> ${result.actual}
                    </div>
                `;
            });
            
            element.innerHTML = html;
        }
    </script>
</body>
</html>
